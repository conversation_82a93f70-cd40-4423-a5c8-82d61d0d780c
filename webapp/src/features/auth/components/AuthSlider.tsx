import { useState, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentStep, setCurrentStep } from '@/store/slices/authSlice';
import invoiceImage from '@/assets/images/523416042ce370de632c87cff79e68579797f152.png';
import paymentImage from '@/assets/images/5df50be752c35cdc6e81308c6a4672d206c472e5.png';
import posImage from '@/assets/images/eeb7dfc1e4ff53b18feb7aad13188fdef135c389.png';

interface Slide {
  id: number;
  title: string;
  image: string;
}

const slides: Slide[] = [
  {
    id: 1,
    title: 'Invoicing',
    image: invoiceImage,
  },
  {
    id: 2,
    title: 'Payments',
    image: paymentImage,
  },
  {
    id: 3,
    title: 'POS Terminal',
    image: posImage,
  },
];

export function AuthSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-advance slides every 5 seconds (slightly slower for better UX)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <div
      data-testid="auth-slider"
      className="relative w-full h-full flex flex-col bg-gradient-to-br from-[#0a0a0a] to-[#1a1a1a] overflow-hidden"
    >
      {/* Background gradient effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
      </div>



      {/* Slide content */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full px-8">
        {/* Title */}
        <h2 className="text-white text-2xl font-semibold mb-8 text-center transition-all duration-700 ease-out transform">
          {slides[currentSlide].title}
        </h2>

        {/* Image container */}
        <div className="relative w-full max-w-lg mb-8">
          <div className="relative w-full bg-gradient-to-br from-gray-800/20 to-gray-900/20 rounded-2xl p-6 backdrop-blur-sm">
            <div className="relative w-full h-80 flex items-center justify-center">
              <img
                src={slides[currentSlide].image}
                alt={slides[currentSlide].title}
                className="max-w-full max-h-full object-contain transition-all duration-700 ease-out transform hover:scale-105"
                style={{
                  filter: 'drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3))',
                }}
              />
            </div>
          </div>
        </div>

        {/* Navigation dots */}
        <div className="flex space-x-3">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
              className={`h-2 rounded-full transition-all duration-500 ease-out transform hover:scale-110 ${
                index === currentSlide
                  ? 'w-8 bg-[#C4FF61] shadow-lg shadow-[#C4FF61]/30'
                  : 'w-2 bg-gray-600 hover:bg-gray-500 hover:w-4'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
