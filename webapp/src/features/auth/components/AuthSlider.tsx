import { useState, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentStep, setCurrentStep } from '@/store/slices/authSlice';
import invoiceImage from '@/assets/images/523416042ce370de632c87cff79e68579797f152.png';

interface Slide {
  id: number;
  title: string;
  image: string;
}

const slides: Slide[] = [
  {
    id: 1,
    title: 'Invoicing',
    image: invoiceImage,
  },
  {
    id: 2,
    title: 'Invoicing',
    image: invoiceImage,
  },
  {
    id: 3,
    title: 'Invoicing',
    image: invoiceImage,
  },
];

interface AuthSliderProps {
  activeTab: 'sign-in' | 'sign-up';
  onTabChange: (tab: 'sign-in' | 'sign-up') => void;
}

export function AuthSlider({ activeTab, onTabChange }: AuthSliderProps) {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-advance slides every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <div
      data-testid="auth-slider"
      className="relative w-full h-full flex flex-col bg-gradient-to-br from-[#0a0a0a] to-[#1a1a1a] overflow-hidden"
    >
      {/* Background gradient effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
      </div>

      {/* Header content */}
      <div className="relative z-10 p-8">
        {/* WeFi Logo */}
        <div className="mb-8">
          <div className="text-primary text-3xl font-bold tracking-wide flex items-center gap-2">
            <span className="text-4xl">🅦</span>
            <span>WeFi</span>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex mb-8 bg-[#2a2a2a] rounded-full p-1 max-w-xs">
          <button
            type="button"
            onClick={() => onTabChange('sign-in')}
            className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-all duration-200 ${
              activeTab === 'sign-in'
                ? 'bg-primary text-black shadow-lg'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Sign In
          </button>
          <button
            type="button"
            onClick={() => onTabChange('sign-up')}
            className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-all duration-200 ${
              activeTab === 'sign-up'
                ? 'bg-primary text-black shadow-lg'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Sign Up
          </button>
        </div>
      </div>

      {/* Slide content */}
      <div className="relative z-10 flex flex-col items-center justify-center flex-1 px-8">
        {/* Title */}
        <h2 className="text-white text-2xl font-semibold mb-8 text-center">
          {slides[currentSlide].title}
        </h2>

        {/* Image container */}
        <div className="relative w-full max-w-lg h-80 mb-8">
          <div className="relative w-full h-full overflow-hidden rounded-2xl">
            <img
              src={slides[currentSlide].image}
              alt={slides[currentSlide].title}
              className="w-full h-full object-cover transition-all duration-500 ease-in-out"
            />
          </div>
        </div>

        {/* Navigation dots */}
        <div className="flex space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
              className={`h-2 rounded-full transition-all duration-300 ${
                index === currentSlide
                  ? 'w-6 bg-[#C4FF61]'
                  : 'w-2 bg-gray-600 hover:bg-gray-500'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
