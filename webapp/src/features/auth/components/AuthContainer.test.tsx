import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import authReducer from '@/store/slices/authSlice';
import { AuthContainer } from './AuthContainer';

// Mock all the form components
vi.mock('./LoginForm', () => ({
  LoginForm: ({ onSuccess }: { onSuccess?: () => void }) => (
    <div data-testid="login-form">
      <button onClick={onSuccess}>Login</button>
    </div>
  ),
}));

vi.mock('./AuthSlider', () => ({
  AuthSlider: () => <div data-testid="auth-slider">Auth Slider</div>,
}));

vi.mock('./SignUpForm', () => ({
  SignUpForm: ({ onSuccess }: { onSuccess?: () => void }) => (
    <div data-testid="signup-form">
      <button onClick={onSuccess}>Sign Up</button>
    </div>
  ),
}));

vi.mock('./OTPVerification', () => ({
  OTPVerification: ({ onSuccess, onBack }: { onSuccess?: () => void; onBack?: () => void }) => (
    <div data-testid="otp-verification">
      <button onClick={onBack}>Back</button>
      <button onClick={onSuccess}>Verify</button>
    </div>
  ),
}));

vi.mock('./EmailConfirmed', () => ({
  EmailConfirmed: ({ onContinue }: { onContinue?: () => void }) => (
    <div data-testid="email-confirmed">
      <button onClick={onContinue}>Continue</button>
    </div>
  ),
}));

vi.mock('./WalletCreation', () => ({
  WalletCreation: ({ onSuccess, onBack }: { onSuccess?: () => void; onBack?: () => void }) => (
    <div data-testid="wallet-creation">
      <button onClick={onBack}>Back</button>
      <button onClick={onSuccess}>Create Wallet</button>
    </div>
  ),
}));

vi.mock('./WalletCreated', () => ({
  WalletCreated: ({ onContinue }: { onContinue?: () => void }) => (
    <div data-testid="wallet-created">
      <button onClick={onContinue}>Continue</button>
    </div>
  ),
}));

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        currentStep: 'sign-in',
        signUpEmail: null,
        ...initialState,
      },
    },
  });
};

describe('AuthContainer', () => {
  let store: ReturnType<typeof createMockStore>;
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    store = createMockStore();
  });

  const renderAuthContainer = (props = {}) => {
    return render(
      <Provider store={store}>
        <AuthContainer onSuccess={mockOnSuccess} {...props} />
      </Provider>
    );
  };

  it('renders login form by default', () => {
    renderAuthContainer();
    expect(screen.getByTestId('login-form')).toBeInTheDocument();
  });

  it('renders signup form when currentStep is sign-up', () => {
    store = createMockStore({ currentStep: 'sign-up' });
    renderAuthContainer();
    expect(screen.getByTestId('signup-form')).toBeInTheDocument();
  });

  it('renders OTP verification when currentStep is otp-verification', () => {
    store = createMockStore({ currentStep: 'otp-verification' });
    renderAuthContainer();
    expect(screen.getByTestId('otp-verification')).toBeInTheDocument();
  });

  it('renders email confirmed when currentStep is email-confirmed', () => {
    store = createMockStore({ currentStep: 'email-confirmed' });
    renderAuthContainer();
    expect(screen.getByTestId('email-confirmed')).toBeInTheDocument();
  });

  it('renders wallet creation when currentStep is wallet-creation', () => {
    store = createMockStore({ currentStep: 'wallet-creation' });
    renderAuthContainer();
    expect(screen.getByTestId('wallet-creation')).toBeInTheDocument();
  });

  it('renders wallet created when currentStep is wallet-created', () => {
    store = createMockStore({ currentStep: 'wallet-created' });
    renderAuthContainer();
    expect(screen.getByTestId('wallet-created')).toBeInTheDocument();
  });

  it('calls onSuccess when currentStep is completed', () => {
    store = createMockStore({ currentStep: 'completed' });
    renderAuthContainer();
    expect(mockOnSuccess).toHaveBeenCalled();
  });

  it('shows tabs for sign-in and sign-up steps', () => {
    renderAuthContainer();
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Sign Up' })).toBeInTheDocument();
  });

  it('shows slider for sign-in and sign-up steps', () => {
    renderAuthContainer();
    expect(screen.getByTestId('auth-slider')).toBeInTheDocument();
  });

  it('does not show tabs for other steps', () => {
    store = createMockStore({ currentStep: 'otp-verification' });
    renderAuthContainer();
    expect(screen.queryByText('Sign In')).not.toBeInTheDocument();
    expect(screen.queryByText('Sign Up')).not.toBeInTheDocument();
  });
});
