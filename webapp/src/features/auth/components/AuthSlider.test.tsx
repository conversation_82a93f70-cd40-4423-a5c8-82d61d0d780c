import { render, screen, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AuthSlider } from './AuthSlider';

// Mock the image imports
vi.mock('@/assets/images/523416042ce370de632c87cff79e68579797f152.png', () => ({
  default: 'mocked-invoice-image.png',
}));

vi.mock('@/assets/images/5df50be752c35cdc6e81308c6a4672d206c472e5.png', () => ({
  default: 'mocked-payment-image.png',
}));

vi.mock('@/assets/images/eeb7dfc1e4ff53b18feb7aad13188fdef135c389.png', () => ({
  default: 'mocked-pos-image.png',
}));

describe('AuthSlider', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('renders the slider with initial slide', () => {
    render(<AuthSlider />);

    expect(screen.getByText('Invoicing')).toBeInTheDocument();
    expect(screen.getByAltText('Invoicing')).toBeInTheDocument();
  });

  it('renders navigation dots', () => {
    render(<AuthSlider />);
    
    const dots = screen.getAllByRole('button');
    expect(dots).toHaveLength(3);
    
    // First dot should be active (wider)
    expect(dots[0]).toHaveClass('w-6');
    expect(dots[1]).toHaveClass('w-2');
    expect(dots[2]).toHaveClass('w-2');
  });

  it('automatically advances slides', () => {
    render(<AuthSlider />);
    
    const dots = screen.getAllByRole('button');
    
    // Initially first slide is active
    expect(dots[0]).toHaveClass('bg-[#C4FF61]');
    expect(dots[1]).toHaveClass('bg-gray-600');
    
    // Advance time by 4 seconds
    act(() => {
      vi.advanceTimersByTime(4000);
    });
    
    // Second slide should now be active
    expect(dots[0]).toHaveClass('bg-gray-600');
    expect(dots[1]).toHaveClass('bg-[#C4FF61]');
  });

  it('allows manual navigation via dots', () => {
    render(<AuthSlider />);

    const dots = screen.getAllByRole('button');

    // Click on third dot using fireEvent instead of userEvent
    act(() => {
      dots[2].click();
    });

    // Third slide should be active
    expect(dots[0]).toHaveClass('bg-gray-600');
    expect(dots[1]).toHaveClass('bg-gray-600');
    expect(dots[2]).toHaveClass('bg-[#C4FF61]');
  });

  it('cycles back to first slide after last slide', () => {
    render(<AuthSlider />);
    
    const dots = screen.getAllByRole('button');
    
    // Advance through all slides
    act(() => {
      vi.advanceTimersByTime(4000); // Second slide
    });
    act(() => {
      vi.advanceTimersByTime(4000); // Third slide
    });
    act(() => {
      vi.advanceTimersByTime(4000); // Back to first slide
    });
    
    // Should be back to first slide
    expect(dots[0]).toHaveClass('bg-[#C4FF61]');
    expect(dots[1]).toHaveClass('bg-gray-600');
    expect(dots[2]).toHaveClass('bg-gray-600');
  });

  it('has proper accessibility attributes', () => {
    render(<AuthSlider />);
    
    const dots = screen.getAllByRole('button');
    
    expect(dots[0]).toHaveAttribute('aria-label', 'Go to slide 1');
    expect(dots[1]).toHaveAttribute('aria-label', 'Go to slide 2');
    expect(dots[2]).toHaveAttribute('aria-label', 'Go to slide 3');
  });
});
