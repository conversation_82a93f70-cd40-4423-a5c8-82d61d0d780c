
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectCurrentStep, setCurrentStep } from '@/store/slices/authSlice';
import { LoginForm } from './LoginForm';
import { SignUpForm } from './SignUpForm';
import { OTPVerification } from './OTPVerification';
import { EmailConfirmed } from './EmailConfirmed';
import { WalletCreation } from './WalletCreation';
import { WalletCreated } from './WalletCreated';
import { AuthSlider } from './AuthSlider';

interface AuthContainerProps {
  onSuccess?: () => void;
}

export function AuthContainer({ onSuccess }: AuthContainerProps) {
  const dispatch = useAppDispatch();
  const currentStep = useAppSelector(selectCurrentStep);

  // Sync activeTab with currentStep from Redux
  const activeTab = (currentStep === 'sign-up') ? 'sign-up' : 'sign-in';

  const handleTabChange = (tab: 'sign-in' | 'sign-up') => {
    dispatch(setCurrentStep(tab));
  };

  const handleSignUpSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleOTPSuccess = () => {
    // Step will be automatically updated by Redux
  };

  const handleEmailConfirmedContinue = () => {
    dispatch(setCurrentStep('wallet-creation'));
  };

  const handleWalletCreationSuccess = () => {
    // The step will be automatically updated by Redux based on the createWallet result
    // If user is authenticated, it will go to 'completed', otherwise to 'wallet-created'
  };

  const handleWalletCreatedContinue = () => {
    dispatch(setCurrentStep('completed'));
    onSuccess?.();
  };

  const handleBackToEmailConfirmed = () => {
    dispatch(setCurrentStep('email-confirmed'));
  };

  const handleBackToSignUp = () => {
    dispatch(setCurrentStep('sign-up'));
  };

  // Render different steps based on current step
  const renderStep = () => {
    switch (currentStep) {
      case 'sign-in':
        return <LoginForm onSuccess={onSuccess} />;

      case 'sign-up':
        return <SignUpForm onSuccess={handleSignUpSuccess} />;

      case 'otp-verification':
        return (
          <OTPVerification
            onSuccess={handleOTPSuccess}
            onBack={handleBackToSignUp}
          />
        );

      case 'email-confirmed':
        return <EmailConfirmed onContinue={handleEmailConfirmedContinue} />;

      case 'wallet-creation':
        return (
          <WalletCreation
            onSuccess={handleWalletCreationSuccess}
            onBack={handleBackToEmailConfirmed}
          />
        );

      case 'wallet-created':
        return <WalletCreated onContinue={handleWalletCreatedContinue} />;

      case 'completed':
        onSuccess?.();
        return null;

      default:
        return <LoginForm onSuccess={onSuccess} />;
    }
  };

  // Show slider layout for sign-in and sign-up steps
  const showSliderLayout = currentStep === 'sign-in' || currentStep === 'sign-up';

  // Show card for most steps, but not for wallet creation steps
  const showCard = !['wallet-creation', 'wallet-created'].includes(currentStep);

  if (showSliderLayout) {
    return (
      <div className="min-h-screen w-screen flex bg-[#0a0a0a]">
        {/* Left side - Slider */}
        <div className="flex-1 hidden lg:block">
          <AuthSlider activeTab={activeTab} onTabChange={handleTabChange} />
        </div>

        {/* Right side - Form */}
        <div className="flex-1 flex items-center justify-center p-8 bg-[#0a0a0a]">
          <div className="w-full max-w-md">
            {/* Welcome Text */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-semibold text-white mb-2">
                {activeTab === 'sign-up' ? 'Sign Up' : 'Sign In'}
              </h1>
              <p className="text-gray-400 text-sm">
                {activeTab === 'sign-in'
                  ? 'Sign in using your email'
                  : 'Create a new account'
                }
              </p>
            </div>

            {/* Render Current Step - No card wrapper */}
            <div className="w-full">
              {renderStep()}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      {/* WeFi Logo */}
      <div className="text-center mb-8">
        <div className="text-primary text-3xl font-bold tracking-wide flex items-center justify-center gap-2">
          <span className="text-4xl">🅦</span>
          <span>WeFi</span>
        </div>
      </div>

      {/* Auth Card or Direct Content */}
      {showCard ? (
        <div className="bg-[#1a1a1a] border border-gray-800 rounded-2xl p-8 w-full max-w-[400px] flex flex-col lg:min-w-[400px]">
          <div className="flex flex-col h-full">
            {/* Render Current Step */}
            <div className="flex-1 flex flex-col justify-center">
              {renderStep()}
            </div>
          </div>
        </div>
      ) : (
        /* Direct rendering for wallet steps */
        renderStep()
      )}
    </div>
  );
}
